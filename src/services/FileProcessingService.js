import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import { PDFDocument } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import sharp from 'sharp';
import { fromPath } from 'pdf2pic';
import logger from '../config/logger.js';
import { S3Service } from './S3Service.js';
import { LLMService } from './LLMService.js';
import { PineconeService } from './PineconeService.js';

/**
 * Optimized File Processing Service
 * Uses lighter alternatives while maintaining all functionality
 * - Replaces pdf2pic with canvas-based PDF processing
 * - Maintains image processing with sharp
 * - Adds fallback mechanisms for reliability
 */

/**
 * @typedef {Object} ProcessedFile
 * @property {string} type - File type (image, document, etc.)
 * @property {string|Array} content - Processed content
 * @property {Object} metadata - File metadata
 * @property {string} filePath - S3 file path
 * @property {string} [extractedContent] - Raw extracted content for Pinecone
 */

/**
 * @typedef {Object} FileAttachment
 * @property {Buffer} buffer - File buffer
 * @property {string} originalname - Original filename
 * @property {string} mimetype - MIME type
 * @property {number} size - File size in bytes
 */

export class FileProcessingService {
  static MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  static SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp'
  ];
  static SUPPORTED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/plain',
    'text/markdown',
    'application/json',
    'text/javascript',
    'application/javascript',
    'text/x-python',
    'application/x-python-code',
    'text/x-java-source',
    'text/x-c',
    'text/x-c++',
    'text/x-csharp',
    'text/x-php',
    'text/x-ruby',
    'text/x-go',
    'text/x-rust',
    'text/x-swift',
    'text/x-kotlin',
    'text/x-scala',
    'text/x-typescript',
    'application/typescript',
    'text/css',
    'text/html',
    'application/xml',
    'text/xml',
    'application/yaml',
    'text/yaml'
  ];

  /**
   * Check if file is text or code file
   * @param {string} mimetype - File MIME type
   * @returns {boolean} Whether file is text or code
   */
  static isTextOrCodeFile(mimetype) {
    const textTypes = [
      'text/plain',
      'text/markdown',
      'application/json',
      'text/javascript',
      'application/javascript',
      'text/x-python',
      'application/x-python-code',
      'text/x-java-source',
      'text/x-c',
      'text/x-c++',
      'text/x-csharp',
      'text/x-php',
      'text/x-ruby',
      'text/x-go',
      'text/x-rust',
      'text/x-swift',
      'text/x-kotlin',
      'text/x-scala',
      'text/x-typescript',
      'application/typescript',
      'text/css',
      'text/html',
      'application/xml',
      'text/xml',
      'application/yaml',
      'text/yaml'
    ];
    return textTypes.includes(mimetype);
  }

  /**
   * Process uploaded file
   * @param {FileAttachment} file - Uploaded file
   * @param {string} userId - User ID
   * @param {string} [sessionId] - Session ID for content storage (used as namespace for Pinecone)
   * @returns {Promise<ProcessedFile>} Processed file result
   */
  static async processFile(file, userId, sessionId = null) {
    if (!file || !file.buffer) {
      throw new Error('Invalid file: missing file buffer');
    }

    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum allowed size of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    const supportedTypes = [...this.SUPPORTED_IMAGE_TYPES, ...this.SUPPORTED_DOCUMENT_TYPES];
    if (!supportedTypes.includes(file.mimetype)) {
      throw new Error(`Unsupported file type: ${file.mimetype}`);
    }

    logger.info(`Processing file: ${file.originalname} (${file.mimetype}, ${file.size} bytes)`);

    try {
      // Generate unique filename and upload to S3
      const timestamp = Date.now();
      const fileExtension = path.extname(file.originalname);
      const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
      const s3Key = `chat-attachments/${userId}/${timestamp}_${sanitizedName}`;

      const fileStorage = await S3Service.uploadFile(file.buffer, s3Key, file.mimetype);

      const metadata = {
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        uploadedAt: new Date().toISOString(),
        userId: userId,
        s3Key: fileStorage.s3Key,
        s3Bucket: fileStorage.s3Bucket
      };

      let processedFile;

      // Process images
      if (this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype)) {
        processedFile = await this.processImage(file, metadata, fileStorage.filePath);
      }
      // Process PDF with enhanced RAG method
      else if (file.mimetype === 'application/pdf') {
        processedFile = await this.processPDFWithRAG(file, metadata, fileStorage.filePath, sessionId);
      }
      // Process Word documents
      else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        processedFile = await this.processWordDocument(file, metadata, fileStorage.filePath);
      }
      // Process Excel files
      else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        processedFile = await this.processExcelFile(file, metadata, fileStorage.filePath);
      }
      // Process text files and code files
      else if (this.isTextOrCodeFile(file.mimetype)) {
        processedFile = await this.processTextFile(file, metadata, fileStorage.filePath);
      }
      else {
        throw new Error(`Unsupported file type: ${file.mimetype}`);
      }

      // Store content in Pinecone if sessionId is provided and content is extractable
      if (sessionId && processedFile.extractedContent) {
        try {
          await PineconeService.storeFileContent(
              processedFile.extractedContent,
              {
                sessionId,
                fileName: file.originalname,
                fileType: file.mimetype,
                userId: userId,
                s3Key: fileStorage.s3Key
              }
          );
          logger.info(`Stored file content in Pinecone for session: ${sessionId}`);
        } catch (pineconeError) {
          logger.warn(`Failed to store content in Pinecone: ${pineconeError.message}`);
          // Don't fail the entire process if Pinecone storage fails
        }
      }

      logger.info(`Successfully processed file: ${file.originalname}`);
      return processedFile;

    } catch (error) {
      logger.error(`Error processing file ${file.originalname}:`, error);
      throw new Error(`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process image files
   * @param {FileAttachment} file - Image file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed image
   */
  static async processImage(file, metadata, filePath) {
    try {
      // Optimize image if needed (compress large images)
      let processedBuffer = file.buffer;

      if (file.size > 1024 * 1024) { // If larger than 1MB, compress
        processedBuffer = await sharp(file.buffer)
            .jpeg({ quality: 85 })
            .toBuffer();

        logger.info(`Compressed image ${file.originalname} from ${file.size} to ${processedBuffer.length} bytes`);
      }

      const base64Image = processedBuffer.toString('base64');
      const mimeType = file.mimetype === 'image/jpg' ? 'image/jpeg' : file.mimetype;

      return {
        type: 'image',
        content: [{
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${base64Image}`
          }
        }],
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing image ${file.originalname}:`, error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF files with optimized method (using canvas instead of pdf2pic)
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @param {string} [sessionId] - Session ID for content storage
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDFOptimized(file, metadata, filePath, sessionId = null) {
    try {
      // Load PDF document to get page count
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      logger.info(`Processing PDF: ${file.originalname} with ${pageCount} pages (optimized mode)`);

      // Try to extract text content using pdf-lib first
      let extractedContent = '';
      try {
        // Basic text extraction (pdf-lib has limited text extraction capabilities)
        extractedContent = await this.extractTextFromPDF(pdfDoc, file.originalname);
      } catch (textError) {
        logger.warn(`Text extraction failed for ${file.originalname}, using fallback method`);
        extractedContent = `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nNote: Text extraction not available for this PDF. Please use the chat to ask specific questions about the document.`;
      }

      // If we have meaningful content, try to convert first few pages to images for LLM processing
      if (pageCount <= 5) { // Only for small PDFs to avoid resource issues
        try {
          const images = await this.convertPDFToImagesOptimized(file.buffer, Math.min(pageCount, 3));
          if (images.length > 0) {
            const llmExtractedContent = await this.extractTextFromPDFImages(images, file.originalname);
            if (llmExtractedContent && llmExtractedContent.length > extractedContent.length) {
              extractedContent = llmExtractedContent;
            }
          }
        } catch (imageError) {
          logger.warn(`Image conversion failed for ${file.originalname}:`, imageError.message);
          // Continue with basic text extraction
        }
      }

      return {
        type: 'document',
        content: `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nExtracted Content:\n${extractedContent}`,
        extractedContent: extractedContent,
        metadata: {
          ...metadata,
          pageCount,
          hasExtractedContent: extractedContent.length > 100,
          processingMode: 'optimized'
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error processing PDF ${file.originalname}:`, error);

      // Fallback to basic PDF processing
      return await this.processPDFBasic(file, metadata, filePath);
    }
  }

  /**
   * Convert PDF to images using canvas (lighter alternative to pdf2pic)
   * @param {Buffer} pdfBuffer - PDF file buffer
   * @param {number} maxPages - Maximum pages to convert
   * @returns {Promise<Array>} Array of image buffers
   */
  static async convertPDFToImagesOptimized(pdfBuffer, maxPages = 3) {
    try {
      // Dynamic import to avoid loading if not available
      const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.mjs');
      const { createCanvas } = await import('@napi-rs/canvas');

      // Convert Buffer to Uint8Array as required by PDF.js
      const uint8Array = new Uint8Array(pdfBuffer);

      // Load PDF
      const pdf = await pdfjsLib.getDocument({ data: uint8Array }).promise;
      const images = [];

      const pagesToProcess = Math.min(maxPages, pdf.numPages);

      for (let pageNum = 1; pageNum <= pagesToProcess; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const viewport = page.getViewport({ scale: 1.5 });

          // Create canvas
          const canvas = createCanvas(viewport.width, viewport.height);
          const context = canvas.getContext('2d');

          // Render page to canvas
          await page.render({
            canvasContext: context,
            viewport: viewport
          }).promise;

          // Convert to buffer
          const imageBuffer = canvas.toBuffer('image/jpeg', { quality: 0.8 });
          images.push(imageBuffer);

        } catch (pageError) {
          logger.warn(`Failed to convert page ${pageNum}:`, pageError.message);
          continue;
        }
      }

      logger.info(`Converted ${images.length} pages to images using canvas`);
      return images;

    } catch (error) {
      logger.error('Error converting PDF to images with canvas:', error);
      throw error;
    }
  }

  /**
   * Extract text from PDF using pdf-lib (basic extraction)
   * @param {PDFDocument} pdfDoc - PDF document
   * @param {string} fileName - File name for logging
   * @returns {Promise<string>} Extracted text
   */
  static async extractTextFromPDF(pdfDoc, fileName) {
    try {
      // pdf-lib has limited text extraction capabilities
      // This is a basic implementation - for better text extraction,
      // consider using pdf-parse or similar libraries

      const pageCount = pdfDoc.getPageCount();
      let extractedText = '';

      // For now, return basic info since pdf-lib doesn't have built-in text extraction
      extractedText = `PDF Document: ${fileName}\nPages: ${pageCount}\n\nNote: Advanced text extraction requires additional processing. The document has been uploaded and can be referenced in chat conversations.`;

      return extractedText;
    } catch (error) {
      logger.error('Error extracting text from PDF:', error);
      throw error;
    }
  }

  /**
   * Extract text content from PDF images using LLM
   * @param {Array} images - Array of image buffers
   * @param {string} fileName - Original file name
   * @returns {Promise<string>} Extracted text content
   */
  static async extractTextFromPDFImages(images, fileName) {
    try {
      let extractedText = '';

      for (let i = 0; i < images.length; i++) {
        const imageBuffer = images[i];

        // Convert image buffer to base64
        const base64Image = imageBuffer.toString('base64');
        const imageContent = {
          type: 'image_url',
          image_url: {
            url: `data:image/jpeg;base64,${base64Image}`
          }
        };

        const prompt = `Extract all text content from this PDF page image. Preserve formatting and structure as much as possible. Return only the extracted text without any additional commentary.`;

        try {
          const pageText = await LLMService.generateResponse(
              prompt,
              'gpt-4o-mini', // Use vision-capable model
              'You are a text extraction assistant. Extract text accurately from images.',
              [],
              {
                type: 'image',
                content: [imageContent],
                metadata: { originalName: fileName }
              }
          );

          extractedText += `\n--- Page ${i + 1} ---\n${pageText}\n`;
        } catch (pageError) {
          logger.warn(`Failed to extract text from page ${i + 1} of ${fileName}:`, pageError);
          extractedText += `\n--- Page ${i + 1} ---\n[Text extraction failed for this page]\n`;
        }
      }

      logger.info(`Extracted text from ${images.length} pages of ${fileName}`);
      return extractedText.trim();
    } catch (error) {
      logger.error('Error extracting text from PDF images:', error);
      throw error;
    }
  }

  /**
   * Basic PDF processing fallback
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDFBasic(file, metadata, filePath) {
    try {
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      const content = `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nNote: This PDF has been uploaded and stored. For detailed content extraction, please use the chat to ask specific questions about the document.`;

      return {
        type: 'document',
        content: content,
        extractedContent: content,
        metadata: {
          ...metadata,
          pageCount,
          processingMode: 'basic'
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error in basic PDF processing for ${file.originalname}:`, error);
      throw error;
    }
  }

  // ... (other methods remain the same as in the lightweight version)
  // Word, Excel, and Text processing methods are unchanged

  /**
   * Process Word documents
   * @param {FileAttachment} file - Word document
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed document
   */
  static async processWordDocument(file, metadata, filePath) {
    try {
      const result = await mammoth.convertToHtml({ buffer: file.buffer });

      if (result.messages.length > 0) {
        logger.warn(`Word document conversion warnings for ${file.originalname}:`, result.messages);
      }

      // Extract plain text from HTML for better readability
      const plainText = result.value.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();

      return {
        type: 'document',
        content: `Word Document: ${file.originalname}\n\nContent:\n${plainText}`,
        extractedContent: plainText,
        metadata: {
          ...metadata,
          wordCount: plainText.split(/\s+/).length,
          hasImages: result.value.includes('<img')
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Word document ${file.originalname}:`, error);
      throw new Error(`Failed to process Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Excel files
   * @param {FileAttachment} file - Excel file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed spreadsheet
   */
  static async processExcelFile(file, metadata, filePath) {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetNames = workbook.SheetNames;

      let content = `Excel File: ${file.originalname}\nSheets: ${sheetNames.join(', ')}\n\n`;
      let extractedContent = '';

      // Process each sheet
      for (const sheetName of sheetNames.slice(0, 5)) { // Limit to first 5 sheets
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        content += `Sheet: ${sheetName}\n`;
        content += `Rows: ${jsonData.length}\n`;

        // Add first few rows as sample
        if (jsonData.length > 0) {
          const sampleRows = jsonData.slice(0, 10).map(row => row.join('\t')).join('\n');
          content += `Sample data:\n${sampleRows}\n\n`;
          extractedContent += `${sheetName}:\n${sampleRows}\n\n`;
        }
      }

      return {
        type: 'document',
        content,
        extractedContent,
        metadata: {
          ...metadata,
          sheetCount: sheetNames.length,
          sheetNames
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Excel file ${file.originalname}:`, error);
      throw new Error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process text files
   * @param {FileAttachment} file - Text file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed text
   */
  static async processTextFile(file, metadata, filePath) {
    try {
      const content = file.buffer.toString('utf-8');

      return {
        type: 'document',
        content: `Text File: ${file.originalname}\n\nContent:\n${content}`,
        extractedContent: content,
        metadata: {
          ...metadata,
          characterCount: content.length,
          lineCount: content.split('\n').length
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error processing text file ${file.originalname}:`, error);
      throw new Error(`Failed to process text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF with enhanced RAG functionality
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @param {string} sessionId - Session ID for namespace
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDFWithRAG(file, metadata, filePath, sessionId) {
    try {
      logger.info(`Processing PDF with RAG: ${file.originalname}`);

      // Step 1: Convert PDF pages to images using temporary storage
      const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'pdf-processing-'));
      const tempPdfPath = path.join(tempDir, `temp_${Date.now()}.pdf`);

      try {
        // Write PDF to temporary file
        await fs.writeFile(tempPdfPath, file.buffer);

        // Convert PDF pages to images
        const images = await this.convertPDFToImages(tempPdfPath);

        if (images.length === 0) {
          throw new Error('No pages could be converted from PDF');
        }

        // Step 2: Convert images to base64 and extract text using GPT-4o-mini
        let extractedContent = '';
        const extractionPrompt = `Extract all the given content from the image.
Do not miss, rephrase or skip content`;

        for (let i = 0; i < images.length; i++) {
          const imageBase64 = images[i];

          try {
            logger.info(`Extracting text from page ${i + 1} of ${file.originalname}`);

            const pageText = await LLMService.generateResponse(
              extractionPrompt,
              'gpt-4o-mini', // Use GPT-4o-mini as specified
              'You are a text extraction assistant. Extract text accurately from images.',
              [],
              {
                type: 'image',
                content: [{
                  type: 'image_url',
                  image_url: {
                    url: `data:image/png;base64,${imageBase64}`
                  }
                }],
                metadata: { originalName: file.originalname, pageNumber: i + 1 }
              }
            );

            extractedContent += `\n--- Page ${i + 1} ---\n${pageText}\n`;

          } catch (pageError) {
            logger.warn(`Failed to extract text from page ${i + 1} of ${file.originalname}:`, pageError);
            extractedContent += `\n--- Page ${i + 1} ---\n[Text extraction failed for this page]\n`;
          }
        }

        // Step 3: Store content in Pinecone using sessionId as namespace
        if (sessionId && extractedContent && extractedContent.trim().length > 0) {
          try {
            logger.info(`Storing PDF content in Pinecone. Content length: ${extractedContent.length}, SessionId: ${sessionId}`);

            await PineconeService.storeFileContent(
              extractedContent,
              {
                sessionId,
                fileName: file.originalname,
                fileType: file.mimetype,
                userId: metadata.userId,
                s3Key: metadata.s3Key,
                pageCount: images.length,
                processingMethod: 'rag_enhanced'
              }
            );
            logger.info(`Successfully stored PDF content in Pinecone namespace: ${sessionId}`);
          } catch (pineconeError) {
            logger.error(`Failed to store PDF content in Pinecone: ${pineconeError.message}`, pineconeError);
          }
        } else {
          logger.warn(`Skipping Pinecone storage - SessionId: ${sessionId}, Content length: ${extractedContent?.length || 0}`);
        }

        return {
          type: 'document',
          content: `PDF Document: ${file.originalname}\nPages: ${images.length}\n\nContent extracted and stored for semantic search.`,
          extractedContent: extractedContent,
          metadata: {
            ...metadata,
            pageCount: images.length,
            processingMode: 'rag_enhanced',
            hasSemanticSearch: !!sessionId
          },
          filePath
        };

      } finally {
        // Clean up temporary files
        try {
          await fs.rm(tempDir, { recursive: true, force: true });
        } catch (cleanupError) {
          logger.warn(`Failed to clean up temporary directory: ${cleanupError.message}`);
        }
      }

    } catch (error) {
      logger.error(`Error processing PDF with RAG ${file.originalname}:`, error);
      throw new Error(`Failed to process PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert PDF to images using pdf2pic
   * @param {string} pdfPath - Path to PDF file
   * @returns {Promise<Array<string>>} Array of base64 image strings
   */
  static async convertPDFToImages(pdfPath) {
    try {
      const convert = fromPath(pdfPath, {
        density: 150,           // DPI
        saveFilename: "page",
        savePath: path.dirname(pdfPath),
        format: "png",
        width: 1200,
        height: 1600
      });

      // Convert all pages
      const results = await convert.bulk(-1);
      const base64Images = [];

      for (const result of results) {
        if (result.path) {
          // Read the image file and convert to base64
          const imageBuffer = await fs.readFile(result.path);
          const base64 = imageBuffer.toString('base64');
          base64Images.push(base64);

          // Clean up the temporary image file
          try {
            await fs.unlink(result.path);
          } catch (unlinkError) {
            logger.warn(`Failed to delete temporary image: ${unlinkError.message}`);
          }
        }
      }

      logger.info(`Converted ${base64Images.length} pages from PDF to images`);
      return base64Images;

    } catch (error) {
      logger.error(`Error converting PDF to images:`, error);
      throw new Error(`PDF to image conversion failed: ${error.message}`);
    }
  }
}
